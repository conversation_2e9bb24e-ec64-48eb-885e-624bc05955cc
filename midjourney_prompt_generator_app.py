"""
Midjourney Prompt Generator Application

This application provides a GUI for analyzing scripts with ChatGPT,
generating Midjourney prompts, editing them, and sending them to the Midjourney API.

Features:
- Script input and analysis
- ChatGPT integration for prompt generation
- Prompt editing and preview
- Midjourney API integration
- Prompt and image saving
"""

import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import json
import time
from datetime import datetime
from dotenv import load_dotenv
import shutil

# Load environment variables from .env file if it exists
load_dotenv()

# Import required packages
from openai import OpenAI
import requests

# Import local modules
from midjourney_prompt_generator import MidjourneyPromptGenerator

class MidjourneyPromptGeneratorApp:
    """Main application class for Midjourney Prompt Generator"""

    def __init__(self, root):
        """Initialize the application"""
        self.root = root
        self.root.title("Midjourney Prompt Generator")
        self.root.geometry("900x700")
        self.root.resizable(True, True)

        # Set style
        self.style = ttk.Style()
        self.style.configure("TButton", padding=6, relief="flat", background="#ccc")
        self.style.configure("TLabel", padding=6)
        self.style.configure("TEntry", padding=6)

        # Create main frame
        main_frame = ttk.Frame(root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Create notebook (tabs)
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create tabs
        self.setup_tab = ttk.Frame(self.notebook)
        self.script_tab = ttk.Frame(self.notebook)
        self.analysis_tab = ttk.Frame(self.notebook)
        self.prompt_tab = ttk.Frame(self.notebook)

        self.notebook.add(self.setup_tab, text="Setup")
        self.notebook.add(self.script_tab, text="Script")
        self.notebook.add(self.analysis_tab, text="Analysis")
        self.notebook.add(self.prompt_tab, text="Prompts")

        # Setup each tab
        self.setup_setup_tab()
        self.setup_script_tab()
        self.setup_analysis_tab()
        self.setup_prompt_tab()

        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # Initialize variables
        self.openai_client = None
        self.prompt_generator = None
        self.script_analysis = None
        self.generated_prompts = None
        self.current_prompt_index = 0
        self.load_settings()

    def setup_setup_tab(self):
        """Setup the Setup tab"""
        # API Keys section
        api_frame = ttk.LabelFrame(self.setup_tab, text="API Keys")
        api_frame.pack(fill=tk.X, padx=5, pady=5)

        # OpenAI API Key
        openai_label = ttk.Label(api_frame, text="OpenAI API Key:")
        openai_label.grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)

        self.openai_api_key_var = tk.StringVar()
        openai_entry = ttk.Entry(api_frame, textvariable=self.openai_api_key_var, width=40, show="*")
        openai_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        # OpenAI Base URL
        openai_base_url_label = ttk.Label(api_frame, text="OpenAI Base URL:")
        openai_base_url_label.grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)

        self.openai_base_url_var = tk.StringVar(value="https://api.openai.com/v1")
        openai_base_url_entry = ttk.Entry(api_frame, textvariable=self.openai_base_url_var, width=40)
        openai_base_url_entry.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        # Midjourney API Key
        midjourney_label = ttk.Label(api_frame, text="Midjourney API Key:")
        midjourney_label.grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)

        self.midjourney_api_key_var = tk.StringVar()
        midjourney_entry = ttk.Entry(api_frame, textvariable=self.midjourney_api_key_var, width=40, show="*")
        midjourney_entry.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

        # Midjourney Base URL
        midjourney_base_url_label = ttk.Label(api_frame, text="Midjourney API URL:")
        midjourney_base_url_label.grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)

        self.midjourney_base_url_var = tk.StringVar(value="https://api.midjourney.com/v1")
        midjourney_base_url_entry = ttk.Entry(api_frame, textvariable=self.midjourney_base_url_var, width=40)
        midjourney_base_url_entry.grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)

        # Output directory
        output_frame = ttk.LabelFrame(self.setup_tab, text="Output Settings")
        output_frame.pack(fill=tk.X, padx=5, pady=5)

        # Prompts directory
        prompts_dir_label = ttk.Label(output_frame, text="Prompts Directory:")
        prompts_dir_label.grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)

        self.prompts_dir_var = tk.StringVar()
        prompts_dir_entry = ttk.Entry(output_frame, textvariable=self.prompts_dir_var, width=40)
        prompts_dir_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        prompts_browse_button = ttk.Button(output_frame, text="Browse",
                                          command=lambda: self.browse_directory(self.prompts_dir_var))
        prompts_browse_button.grid(row=0, column=2, padx=5, pady=5)

        # Images directory
        images_dir_label = ttk.Label(output_frame, text="Images Directory:")
        images_dir_label.grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)

        self.images_dir_var = tk.StringVar()
        images_dir_entry = ttk.Entry(output_frame, textvariable=self.images_dir_var, width=40)
        images_dir_entry.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        images_browse_button = ttk.Button(output_frame, text="Browse",
                                         command=lambda: self.browse_directory(self.images_dir_var))
        images_browse_button.grid(row=1, column=2, padx=5, pady=5)

        # Buttons frame
        buttons_frame = ttk.Frame(self.setup_tab)
        buttons_frame.pack(fill=tk.X, padx=5, pady=10)

        # Save settings button
        save_button = ttk.Button(buttons_frame, text="Save Settings", command=self.save_settings)
        save_button.pack(side=tk.LEFT, padx=5, pady=5)

        # Test connections button
        test_button = ttk.Button(buttons_frame, text="Test Connections", command=self.test_connections)
        test_button.pack(side=tk.LEFT, padx=5, pady=5)

    def setup_script_tab(self):
        """Setup the Script tab"""
        # Script input
        script_frame = ttk.LabelFrame(self.script_tab, text="Script Input")
        script_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Script text area
        self.script_text = scrolledtext.ScrolledText(script_frame, wrap=tk.WORD, height=20)
        self.script_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Load script button
        load_button = ttk.Button(self.script_tab, text="Load Script from File", command=self.load_script)
        load_button.pack(side=tk.LEFT, padx=5, pady=5)

        # Save script button
        save_button = ttk.Button(self.script_tab, text="Save Script to File", command=self.save_script)
        save_button.pack(side=tk.LEFT, padx=5, pady=5)

        # Clear script button
        clear_button = ttk.Button(self.script_tab, text="Clear Script", command=self.clear_script)
        clear_button.pack(side=tk.LEFT, padx=5, pady=5)

        # Analyze script button
        analyze_button = ttk.Button(self.script_tab, text="Analyze Script", command=self.analyze_script)
        analyze_button.pack(side=tk.LEFT, padx=5, pady=5)

    def setup_analysis_tab(self):
        """Setup the Analysis tab"""
        # Options frame
        options_frame = ttk.LabelFrame(self.analysis_tab, text="Analysis Options")
        options_frame.pack(fill=tk.X, padx=5, pady=5)

        # ChatGPT model selection
        model_label = ttk.Label(options_frame, text="ChatGPT Model:")
        model_label.grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)

        self.chatgpt_model_var = tk.StringVar(value="gpt-4o")
        model_dropdown = ttk.Combobox(options_frame, textvariable=self.chatgpt_model_var, width=15)
        model_dropdown['values'] = ["gpt-4o", "gpt-4-turbo", "gpt-3.5-turbo"]
        model_dropdown.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        # Style preset selection
        style_label = ttk.Label(options_frame, text="Style Preset:")
        style_label.grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)

        self.style_preset_var = tk.StringVar(value="minimalist")
        style_dropdown = ttk.Combobox(options_frame, textvariable=self.style_preset_var, width=15)
        style_dropdown['values'] = ["minimalist", "sketch", "watercolor", "3d_render", "comic", "photorealistic"]
        style_dropdown.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        # Analysis results frame
        results_frame = ttk.LabelFrame(self.analysis_tab, text="Analysis Results")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Analysis results text area
        self.analysis_text = scrolledtext.ScrolledText(results_frame, wrap=tk.WORD, height=20)
        self.analysis_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Buttons frame
        buttons_frame = ttk.Frame(self.analysis_tab)
        buttons_frame.pack(fill=tk.X, padx=5, pady=5)

        # Generate prompts button
        generate_button = ttk.Button(buttons_frame, text="Generate Midjourney Prompts",
                                    command=self.generate_prompts)
        generate_button.pack(side=tk.LEFT, padx=5, pady=5)

        # Save analysis button
        save_analysis_button = ttk.Button(buttons_frame, text="Save Analysis",
                                         command=self.save_analysis)
        save_analysis_button.pack(side=tk.LEFT, padx=5, pady=5)

    def setup_prompt_tab(self):
        """Setup the Prompt tab"""
        # Prompt navigation frame
        nav_frame = ttk.Frame(self.prompt_tab)
        nav_frame.pack(fill=tk.X, padx=5, pady=5)

        # Previous prompt button
        prev_button = ttk.Button(nav_frame, text="← Previous", command=self.previous_prompt)
        prev_button.pack(side=tk.LEFT, padx=5, pady=5)

        # Prompt counter
        self.prompt_counter_var = tk.StringVar(value="Prompt 0/0")
        prompt_counter = ttk.Label(nav_frame, textvariable=self.prompt_counter_var)
        prompt_counter.pack(side=tk.LEFT, padx=20, pady=5)

        # Next prompt button
        next_button = ttk.Button(nav_frame, text="Next →", command=self.next_prompt)
        next_button.pack(side=tk.LEFT, padx=5, pady=5)

        # Prompt display frame
        prompt_display_frame = ttk.LabelFrame(self.prompt_tab, text="Prompt")
        prompt_display_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Original text label
        original_label = ttk.Label(prompt_display_frame, text="Original Text:")
        original_label.pack(anchor=tk.W, padx=5, pady=2)

        # Original text display
        self.original_text_var = tk.StringVar()
        original_text = ttk.Label(prompt_display_frame, textvariable=self.original_text_var, wraplength=850)
        original_text.pack(fill=tk.X, padx=5, pady=2)

        # Prompt editing
        prompt_label = ttk.Label(prompt_display_frame, text="Midjourney Prompt:")
        prompt_label.pack(anchor=tk.W, padx=5, pady=2)

        # Prompt text area
        self.prompt_text = scrolledtext.ScrolledText(prompt_display_frame, wrap=tk.WORD, height=10)
        self.prompt_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Description label
        description_label = ttk.Label(prompt_display_frame, text="Description:")
        description_label.pack(anchor=tk.W, padx=5, pady=2)

        # Description text area
        self.description_text = scrolledtext.ScrolledText(prompt_display_frame, wrap=tk.WORD, height=5)
        self.description_text.pack(fill=tk.X, padx=5, pady=5)

        # Buttons frame
        buttons_frame = ttk.Frame(self.prompt_tab)
        buttons_frame.pack(fill=tk.X, padx=5, pady=5)

        # Save prompt button
        save_prompt_button = ttk.Button(buttons_frame, text="Save Prompt", command=self.save_current_prompt)
        save_prompt_button.pack(side=tk.LEFT, padx=5, pady=5)

        # Send to Midjourney button
        send_button = ttk.Button(buttons_frame, text="Send to Midjourney", command=self.send_to_midjourney)
        send_button.pack(side=tk.LEFT, padx=5, pady=5)

        # Save all prompts button
        save_all_button = ttk.Button(buttons_frame, text="Save All Prompts", command=self.save_all_prompts)
        save_all_button.pack(side=tk.LEFT, padx=5, pady=5)

    def browse_directory(self, var):
        """Browse for a directory and set the variable"""
        directory = filedialog.askdirectory()
        if directory:
            var.set(directory)

    def load_script(self):
        """Load script from file"""
        file_path = filedialog.askopenfilename(filetypes=[("Text Files", "*.txt"), ("All Files", "*.*")])
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    self.script_text.delete(1.0, tk.END)
                    self.script_text.insert(tk.END, content)
                self.log_message(f"Loaded script from {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load script: {str(e)}")

    def save_script(self):
        """Save script to file"""
        file_path = filedialog.asksaveasfilename(defaultextension=".txt",
                                               filetypes=[("Text Files", "*.txt"), ("All Files", "*.*")])
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.script_text.get(1.0, tk.END))
                self.log_message(f"Saved script to {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save script: {str(e)}")

    def clear_script(self):
        """Clear the script text area"""
        if messagebox.askyesno("Confirm", "Are you sure you want to clear the script?"):
            self.script_text.delete(1.0, tk.END)

    def analyze_script(self):
        """Analyze the script using ChatGPT"""
        # Check if OpenAI client is initialized
        if not self.openai_client:
            messagebox.showerror("Error", "OpenAI client not initialized. Please check your API key.")
            return

        # Check if script is provided
        script = self.script_text.get(1.0, tk.END).strip()
        if not script:
            messagebox.showerror("Error", "Please enter a script")
            return

        # Start analysis in a separate thread
        threading.Thread(target=self._analyze_script_thread, args=(script,)).start()

    def _analyze_script_thread(self, script):
        """Thread function for script analysis"""
        self.log_message("Analyzing script with ChatGPT...")
        self.status_var.set("Analyzing script...")

        try:
            # Get selected model
            model = self.chatgpt_model_var.get()

            # Create prompt generator if not already created
            if not self.prompt_generator:
                self.prompt_generator = MidjourneyPromptGenerator(
                    openai_client=self.openai_client,
                    openai_api_key=self.openai_api_key_var.get(),
                    openai_base_url=self.openai_base_url_var.get(),
                    midjourney_api_key=self.midjourney_api_key_var.get(),
                    midjourney_base_url=self.midjourney_base_url_var.get()
                )

            # Analyze script
            self.script_analysis = self.prompt_generator.analyze_script(script, model=model)

            # Display analysis results
            self.analysis_text.delete(1.0, tk.END)
            self.analysis_text.insert(tk.END, json.dumps(self.script_analysis, indent=4))

            # Switch to analysis tab
            self.notebook.select(self.analysis_tab)

            self.log_message(f"Script analysis complete. Found {len(self.script_analysis)} segments.")
            self.status_var.set("Analysis complete")

        except Exception as e:
            self.log_message(f"Error analyzing script: {str(e)}")
            messagebox.showerror("Error", f"Failed to analyze script: {str(e)}")
            self.status_var.set("Analysis failed")

    def generate_prompts(self):
        """Generate Midjourney prompts based on script analysis"""
        # Check if analysis results exist
        if not self.script_analysis:
            messagebox.showerror("Error", "Please analyze the script first")
            return

        # Start generation in a separate thread
        threading.Thread(target=self._generate_prompts_thread).start()

    def _generate_prompts_thread(self):
        """Thread function for prompt generation"""
        self.log_message("Generating Midjourney prompts...")
        self.status_var.set("Generating prompts...")

        try:
            # Get style preset
            style_preset = self.style_preset_var.get()

            # Generate prompts
            self.generated_prompts = self.prompt_generator.generate_midjourney_prompts(
                self.script_analysis, style_preset=style_preset)

            # Display first prompt
            self.current_prompt_index = 0
            self.display_current_prompt()

            # Switch to prompt tab
            self.notebook.select(self.prompt_tab)

            self.log_message(f"Prompt generation complete. Generated {len(self.generated_prompts)} prompts.")
            self.status_var.set("Prompt generation complete")

        except Exception as e:
            self.log_message(f"Error generating prompts: {str(e)}")
            messagebox.showerror("Error", f"Failed to generate prompts: {str(e)}")
            self.status_var.set("Prompt generation failed")

    def display_current_prompt(self):
        """Display the current prompt"""
        if not self.generated_prompts or len(self.generated_prompts) == 0:
            self.prompt_counter_var.set("Prompt 0/0")
            self.original_text_var.set("")
            self.prompt_text.delete(1.0, tk.END)
            self.description_text.delete(1.0, tk.END)
            return

        # Get current prompt
        prompt_data = self.generated_prompts[self.current_prompt_index]

        # Update counter
        self.prompt_counter_var.set(f"Prompt {self.current_prompt_index + 1}/{len(self.generated_prompts)}")

        # Update original text
        self.original_text_var.set(prompt_data.get("segment_text", ""))

        # Update prompt text
        self.prompt_text.delete(1.0, tk.END)
        self.prompt_text.insert(tk.END, prompt_data.get("prompt", ""))

        # Update description text
        self.description_text.delete(1.0, tk.END)
        self.description_text.insert(tk.END, prompt_data.get("description", ""))

    def previous_prompt(self):
        """Go to the previous prompt"""
        if not self.generated_prompts or len(self.generated_prompts) == 0:
            return

        # Save current prompt edits
        self.save_current_prompt_edits()

        # Go to previous prompt
        self.current_prompt_index = max(0, self.current_prompt_index - 1)
        self.display_current_prompt()

    def next_prompt(self):
        """Go to the next prompt"""
        if not self.generated_prompts or len(self.generated_prompts) == 0:
            return

        # Save current prompt edits
        self.save_current_prompt_edits()

        # Go to next prompt
        self.current_prompt_index = min(len(self.generated_prompts) - 1, self.current_prompt_index + 1)
        self.display_current_prompt()

    def save_current_prompt_edits(self):
        """Save edits to the current prompt"""
        if not self.generated_prompts or len(self.generated_prompts) == 0:
            return

        # Get current prompt
        prompt_data = self.generated_prompts[self.current_prompt_index]

        # Update prompt text
        prompt_data["prompt"] = self.prompt_text.get(1.0, tk.END).strip()

        # Update description text
        prompt_data["description"] = self.description_text.get(1.0, tk.END).strip()

    def save_current_prompt(self):
        """Save the current prompt to a file"""
        if not self.generated_prompts or len(self.generated_prompts) == 0:
            messagebox.showerror("Error", "No prompts to save")
            return

        # Save current edits
        self.save_current_prompt_edits()

        # Get prompts directory
        prompts_dir = self.prompts_dir_var.get().strip()
        if not prompts_dir:
            prompts_dir = os.path.join(os.getcwd(), "prompts")

        # Get current prompt
        prompt_data = self.generated_prompts[self.current_prompt_index]

        try:
            # Save prompt
            filepath = self.prompt_generator.save_prompt(prompt_data, prompts_dir)
            self.log_message(f"Saved prompt to {filepath}")
            messagebox.showinfo("Success", f"Saved prompt to {filepath}")
        except Exception as e:
            self.log_message(f"Error saving prompt: {str(e)}")
            messagebox.showerror("Error", f"Failed to save prompt: {str(e)}")

    def save_all_prompts(self):
        """Save all prompts to files"""
        if not self.generated_prompts or len(self.generated_prompts) == 0:
            messagebox.showerror("Error", "No prompts to save")
            return

        # Save current edits
        self.save_current_prompt_edits()

        # Get prompts directory
        prompts_dir = self.prompts_dir_var.get().strip()
        if not prompts_dir:
            prompts_dir = os.path.join(os.getcwd(), "prompts")

        try:
            # Save all prompts
            saved_files = []
            for prompt_data in self.generated_prompts:
                filepath = self.prompt_generator.save_prompt(prompt_data, prompts_dir)
                saved_files.append(filepath)

            self.log_message(f"Saved {len(saved_files)} prompts to {prompts_dir}")
            messagebox.showinfo("Success", f"Saved {len(saved_files)} prompts to {prompts_dir}")
        except Exception as e:
            self.log_message(f"Error saving prompts: {str(e)}")
            messagebox.showerror("Error", f"Failed to save prompts: {str(e)}")

    def send_to_midjourney(self):
        """Send the current prompt to Midjourney API"""
        if not self.generated_prompts or len(self.generated_prompts) == 0:
            messagebox.showerror("Error", "No prompts to send")
            return

        # Check if Midjourney API key is set
        if not self.midjourney_api_key_var.get().strip():
            messagebox.showerror("Error", "Midjourney API key not set")
            return

        # Save current edits
        self.save_current_prompt_edits()

        # Get current prompt
        prompt_data = self.generated_prompts[self.current_prompt_index]
        prompt_text = prompt_data.get("prompt", "").strip()

        if not prompt_text:
            messagebox.showerror("Error", "Prompt is empty")
            return

        # Start sending in a separate thread
        threading.Thread(target=self._send_to_midjourney_thread, args=(prompt_text,)).start()

    def _send_to_midjourney_thread(self, prompt_text):
        """Thread function for sending to Midjourney"""
        self.log_message(f"Sending prompt to Midjourney: {prompt_text[:50]}...")
        self.status_var.set("Sending to Midjourney...")

        try:
            # Send to Midjourney
            response = self.prompt_generator.send_to_midjourney(prompt_text)

            if "error" in response:
                self.log_message(f"Error from Midjourney API: {response.get('message', 'Unknown error')}")
                messagebox.showerror("Error", f"Failed to send to Midjourney: {response.get('message', 'Unknown error')}")
                self.status_var.set("Failed to send to Midjourney")
            else:
                self.log_message(f"Successfully sent to Midjourney. Response: {response}")
                messagebox.showinfo("Success", "Successfully sent to Midjourney")
                self.status_var.set("Sent to Midjourney")

                # Save response to images directory if available
                if "image_url" in response and self.images_dir_var.get().strip():
                    self._save_midjourney_image(response)

        except Exception as e:
            self.log_message(f"Error sending to Midjourney: {str(e)}")
            messagebox.showerror("Error", f"Failed to send to Midjourney: {str(e)}")
            self.status_var.set("Failed to send to Midjourney")

    def _save_midjourney_image(self, response):
        """Save Midjourney image to the images directory"""
        try:
            image_url = response.get("image_url")
            if not image_url:
                return

            # Get images directory
            images_dir = self.images_dir_var.get().strip()
            os.makedirs(images_dir, exist_ok=True)

            # Download image
            image_response = requests.get(image_url, stream=True)
            if image_response.status_code == 200:
                # Create filename
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                image_id = response.get("id", "unknown")
                filename = f"midjourney_{image_id}_{timestamp}.png"
                filepath = os.path.join(images_dir, filename)

                # Save image
                with open(filepath, "wb") as f:
                    for chunk in image_response.iter_content(chunk_size=8192):
                        f.write(chunk)

                self.log_message(f"Saved Midjourney image to {filepath}")
            else:
                self.log_message(f"Failed to download image: {image_response.status_code}")

        except Exception as e:
            self.log_message(f"Error saving Midjourney image: {str(e)}")

    def save_analysis(self):
        """Save analysis results to a file"""
        if not self.script_analysis:
            messagebox.showerror("Error", "No analysis results to save")
            return

        file_path = filedialog.asksaveasfilename(defaultextension=".json",
                                               filetypes=[("JSON Files", "*.json"), ("All Files", "*.*")])
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.script_analysis, f, indent=4)
                self.log_message(f"Saved analysis to {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save analysis: {str(e)}")

    def test_connections(self):
        """Test API connections"""
        # Create a dialog to show test results
        dialog = tk.Toplevel(self.root)
        dialog.title("API Connection Test")
        dialog.geometry("600x400")
        dialog.transient(self.root)
        dialog.grab_set()

        # Create a scrolled text widget to show results
        result_text = scrolledtext.ScrolledText(dialog, wrap=tk.WORD)
        result_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Test OpenAI connection
        result_text.insert(tk.END, "Testing OpenAI connection...\n")
        openai_api_key = self.openai_api_key_var.get().strip()
        openai_base_url = self.openai_base_url_var.get().strip()

        if not openai_api_key:
            result_text.insert(tk.END, "❌ OpenAI API key not provided\n\n")
        else:
            try:
                # Initialize OpenAI client
                if openai_base_url:
                    client = OpenAI(api_key=openai_api_key, base_url=openai_base_url)
                else:
                    client = OpenAI(api_key=openai_api_key)

                # Test API call
                response = client.models.list()

                # Check if response contains models
                if hasattr(response, "data") and len(response.data) > 0:
                    result_text.insert(tk.END, f"✅ OpenAI connection successful\n")
                    result_text.insert(tk.END, f"Found {len(response.data)} models\n\n")
                else:
                    result_text.insert(tk.END, "❌ OpenAI connection failed: No models found\n\n")
            except Exception as e:
                result_text.insert(tk.END, f"❌ OpenAI connection failed: {str(e)}\n\n")

        # Test Midjourney connection
        result_text.insert(tk.END, "Testing Midjourney connection...\n")
        midjourney_api_key = self.midjourney_api_key_var.get().strip()
        midjourney_base_url = self.midjourney_base_url_var.get().strip()

        if not midjourney_api_key:
            result_text.insert(tk.END, "❌ Midjourney API key not provided\n\n")
        else:
            try:
                # Test API connection
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {midjourney_api_key}"
                }

                # Use a simple endpoint that doesn't cost credits
                test_url = f"{midjourney_base_url}/user"
                response = requests.get(test_url, headers=headers)

                if response.status_code == 200:
                    result_text.insert(tk.END, f"✅ Midjourney connection successful\n")
                    result_text.insert(tk.END, f"Response: {response.json()}\n\n")
                else:
                    result_text.insert(tk.END, f"❌ Midjourney connection failed: {response.status_code}\n")
                    result_text.insert(tk.END, f"Response: {response.text}\n\n")
            except Exception as e:
                result_text.insert(tk.END, f"❌ Midjourney connection failed: {str(e)}\n\n")

        # Add close button
        close_button = ttk.Button(dialog, text="Close", command=dialog.destroy)
        close_button.pack(pady=10)

    def get_settings_dir(self):
        """Get the directory where settings should be stored"""
        # Use current directory for simplicity
        settings_dir = os.getcwd()
        return settings_dir

    def load_settings(self):
        """Load settings from file"""
        settings_dir = self.get_settings_dir()
        settings_file = os.path.join(settings_dir, "settings.json")

        if os.path.exists(settings_file):
            try:
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                    # Load API keys
                    self.openai_api_key_var.set(settings.get("openai_api_key", ""))
                    self.midjourney_api_key_var.set(settings.get("midjourney_api_key", ""))

                    # Load URLs
                    if "openai_base_url" in settings:
                        self.openai_base_url_var.set(settings.get("openai_base_url"))
                    else:
                        self.openai_base_url_var.set("https://api.openai.com/v1")

                    if "midjourney_base_url" in settings:
                        self.midjourney_base_url_var.set(settings.get("midjourney_base_url"))
                    else:
                        self.midjourney_base_url_var.set("https://api.midjourney.com/v1")

                    # Load directories
                    self.prompts_dir_var.set(settings.get("prompts_dir", ""))
                    self.images_dir_var.set(settings.get("images_dir", ""))

                    # Initialize API clients
                    self.initialize_apis()

                self.log_message(f"Settings loaded from {settings_file}")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to load settings: {str(e)}")

    def save_settings(self):
        """Save settings to file"""
        settings_dir = self.get_settings_dir()
        settings_file = os.path.join(settings_dir, "settings.json")
        try:
            settings = {
                "openai_api_key": self.openai_api_key_var.get(),
                "midjourney_api_key": self.midjourney_api_key_var.get(),
                "openai_base_url": self.openai_base_url_var.get(),
                "midjourney_base_url": self.midjourney_base_url_var.get(),
                "prompts_dir": self.prompts_dir_var.get(),
                "images_dir": self.images_dir_var.get()
            }

            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=4)

            self.log_message(f"Settings saved to {settings_file}")

            # Initialize API clients
            self.initialize_apis()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save settings: {str(e)}")

    def initialize_apis(self):
        """Initialize API clients"""
        # Initialize OpenAI client
        openai_api_key = self.openai_api_key_var.get().strip()
        openai_base_url = self.openai_base_url_var.get().strip()

        if openai_api_key:
            try:
                # Use the specified base URL if provided
                if openai_base_url:
                    self.openai_client = OpenAI(api_key=openai_api_key, base_url=openai_base_url)
                    self.log_message(f"OpenAI client initialized with custom base URL: {openai_base_url}")
                else:
                    self.openai_client = OpenAI(api_key=openai_api_key)
                    self.log_message("OpenAI client initialized with default base URL")
            except Exception as e:
                self.log_message(f"Error initializing OpenAI client: {str(e)}")
                self.openai_client = None

        # Initialize prompt generator
        if self.openai_client:
            self.prompt_generator = MidjourneyPromptGenerator(
                openai_client=self.openai_client,
                openai_api_key=openai_api_key,
                openai_base_url=openai_base_url,
                midjourney_api_key=self.midjourney_api_key_var.get().strip(),
                midjourney_base_url=self.midjourney_base_url_var.get().strip()
            )
            self.log_message("Midjourney Prompt Generator initialized")

    def log_message(self, message):
        """Add a message to the log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        # Print to console
        print(log_entry.strip())

        # Update status bar
        self.status_var.set(message)

# Main function to run the application
def main():
    root = tk.Tk()
    app = MidjourneyPromptGeneratorApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
